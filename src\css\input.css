/* Import Google Fonts - Sesuai spesifikasi font */
@import url("https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Open+Sans:wght@300;400;500;600;700&display=swap");

/* Import TailwindCSS */
@import "tailwindcss";

/* Custom CSS untuk fitur khusus yang tidak bisa dicapai dengan TailwindCSS */

/* Smooth scrolling untuk seluruh halaman */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Loading animation untuk transisi halaman */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.loading-overlay.active {
  opacity: 1;
  visibility: visible;
}

.dark .loading-overlay {
  background: rgba(24, 24, 24, 0.9);
}

/* Spinner animation */
.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #181818;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.dark .spinner {
  border: 4px solid #2a2a2a;
  border-top: 4px solid #ffffff;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Hamburger menu animation */
.hamburger-line {
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger-active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger-active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 16px 24px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

.notification.show {
  transform: translateX(0);
}

.notification.error {
  background-color: #dc3545;
}

.notification.success {
  background-color: #28a745;
}

.notification.warning {
  background-color: #ffc107;
  color: #212529;
}

/* Fade in animation untuk elemen */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Hover effect untuk navigation underline */
.nav-link {
  position: relative;
  overflow: hidden;
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover::after {
  width: 50%;
}

/* Preview modal styles */
.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.preview-modal.active {
  opacity: 1;
  visibility: visible;
}

.preview-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 90%;
  max-height: 90%;
  overflow-y: auto;
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.preview-modal.active .preview-content {
  transform: scale(1);
}

.dark .preview-content {
  background: #2a2a2a;
  color: white;
}
