const CONFIG = { ANIMATION_DURATION: 300, NOTIFICATION_DURATION: 3000, ITEMS_PER_PAGE: 6, SCROLL_THRESHOLD: 100, }; const STATE = { isDarkMode: false, isMobileMenuOpen: false, currentSkillFilter: "all", currentProjectFilter: "all", currentCertificateFilter: "all", skillsPage: 1, projectsPage: 1, certificatesPage: 1, }; const ELEMENTS = { navbar: null, brandName: null, mobileMenuBtn: null, mobileMenu: null, themeToggleDesktop: null, themeToggleMobile: null, sunIcon: null, moonIcon: null, backToTop: null, loadingOverlay: null, skillsGrid: null, projectsGrid: null, certificatesGrid: null, loadMoreSkills: null, loadMoreProjects: null, loadMoreCertificates: null, skillFilters: null, projectFilters: null, certificateFilters: null, contactForm: null, previewModal: null, closePreview: null, previewTitle: null, previewContent: null, notificationContainer: null, }; function debounce(func, wait) { let timeout; return function executedFunction(...args) { const later = () => { clearTimeout(timeout); func(...args); }; clearTimeout(timeout); timeout = setTimeout(later, wait); }; } function throttle(func, limit) { let inThrottle; return function () { const args = arguments; const context = this; if (!inThrottle) { func.apply(context, args); inThrottle = true; setTimeout(() => (inThrottle = false), limit); } }; } function smoothScrollTo(element) { if (element) { const offsetTop = element.offsetTop - 80; window.scrollTo({ top: offsetTop, behavior: "smooth", }); } } function generateId() { return Date.now().toString(36) + Math.random().toString(36).substr(2); } function disableRightClick() { document.addEventListener("contextmenu", function (e) { e.preventDefault(); showNotification("Klik kanan tidak diizinkan!", "error"); return false; }); } function disableDevTools() { document.addEventListener("keydown", function (e) { if (e.keyCode === 123) { e.preventDefault(); showNotification("Developer tools tidak diizinkan!", "error"); return false; } if (e.ctrlKey && e.shiftKey && e.keyCode === 73) { e.preventDefault(); showNotification("Developer tools tidak diizinkan!", "error"); return false; } if (e.ctrlKey && e.shiftKey && e.keyCode === 74) { e.preventDefault(); showNotification("Developer tools tidak diizinkan!", "error"); return false; } if (e.ctrlKey && e.keyCode === 85) { e.preventDefault(); showNotification("View source tidak diizinkan!", "error"); return false; } if (e.ctrlKey && e.keyCode === 83) { e.preventDefault(); showNotification("Save page tidak diizinkan!", "error"); return false; } }); } function disableCopyPaste() { document.addEventListener("copy", function (e) { e.preventDefault(); showNotification("Copy tidak diizinkan!", "error"); return false; }); document.addEventListener("paste", function (e) { e.preventDefault(); showNotification("Paste tidak diizinkan!", "error"); return false; }); document.addEventListener("cut", function (e) { e.preventDefault(); showNotification("Cut tidak diizinkan!", "error"); return false; }); document.addEventListener("selectstart", function (e) { e.preventDefault(); return false; }); document.addEventListener("dragstart", function (e) { e.preventDefault(); return false; }); } function showNotification(message, type = "info") { const notification = document.createElement("div"); notification.className = `notification ${type}`; notification.textContent = message; ELEMENTS.notificationContainer.appendChild(notification); setTimeout(() => { notification.classList.add("show"); }, 10); setTimeout(() => { notification.classList.remove("show"); setTimeout(() => { if (notification.parentNode) { notification.parentNode.removeChild(notification); } }, CONFIG.ANIMATION_DURATION); }, CONFIG.NOTIFICATION_DURATION); } function initializeTheme() { const savedTheme = localStorage.getItem("theme"); if (savedTheme === "dark") { STATE.isDarkMode = true; document.documentElement.classList.add("dark"); updateThemeIcons(); } else { STATE.isDarkMode = false; document.documentElement.classList.remove("dark"); updateThemeIcons(); } } function toggleTheme() { STATE.isDarkMode = !STATE.isDarkMode; if (STATE.isDarkMode) { document.documentElement.classList.add("dark"); localStorage.setItem("theme", "dark"); } else { document.documentElement.classList.remove("dark"); localStorage.setItem("theme", "light"); } updateThemeIcons(); } function updateThemeIcons() { if (STATE.isDarkMode) { ELEMENTS.sunIcon.classList.remove("hidden"); ELEMENTS.moonIcon.classList.add("hidden"); ELEMENTS.themeToggleMobile.checked = true; } else { ELEMENTS.sunIcon.classList.add("hidden"); ELEMENTS.moonIcon.classList.remove("hidden"); ELEMENTS.themeToggleMobile.checked = false; } } function toggleMobileMenu() { STATE.isMobileMenuOpen = !STATE.isMobileMenuOpen; if (STATE.isMobileMenuOpen) { ELEMENTS.mobileMenu.classList.remove("hidden"); ELEMENTS.mobileMenuBtn.classList.add("hamburger-active"); } else { ELEMENTS.mobileMenu.classList.add("hidden"); ELEMENTS.mobileMenuBtn.classList.remove("hamburger-active"); } } function closeMobileMenu() { STATE.isMobileMenuOpen = false; ELEMENTS.mobileMenu.classList.add("hidden"); ELEMENTS.mobileMenuBtn.classList.remove("hamburger-active"); } function handleNavigation(e) { e.preventDefault(); const href = e.target.getAttribute("href"); if (href && href.startsWith("#")) { const targetElement = document.querySelector(href); if (targetElement) { smoothScrollTo(targetElement); closeMobileMenu(); } } } function handleBrandClick() { window.scrollTo({ top: 0, behavior: "smooth", }); } function updateNavbarOnScroll() { const scrollY = window.scrollY; if (scrollY > CONFIG.SCROLL_THRESHOLD) { ELEMENTS.navbar.classList.add("glass-effect"); if (STATE.isDarkMode) { ELEMENTS.navbar.classList.add("glass-effect-dark"); } ELEMENTS.backToTop.classList.remove("opacity-0", "invisible"); ELEMENTS.backToTop.classList.add("opacity-100", "visible"); } else { ELEMENTS.navbar.classList.remove("glass-effect", "glass-effect-dark"); ELEMENTS.backToTop.classList.add("opacity-0", "invisible"); ELEMENTS.backToTop.classList.remove("opacity-100", "visible"); } } function showLoading() { ELEMENTS.loadingOverlay.classList.add("active"); } function hideLoading() { ELEMENTS.loadingOverlay.classList.remove("active"); } function simulateLoading(duration = 1000) { showLoading(); setTimeout(() => { hideLoading(); }, duration); } const SKILLS_DATA = [ { id: 1, title: "Problem Solving", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "soft", icon: "🧠", }, { id: 2, title: "Communication", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "soft", icon: "💬", }, { id: 3, title: "Leadership", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "soft", icon: "👑", }, { id: 4, title: "JavaScript", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "hard", icon: "⚡", }, { id: 5, title: "Python", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "hard", icon: "🐍", }, { id: 6, title: "Cyber Security", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "hard", icon: "🔒", }, { id: 7, title: "UI/UX Design", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "hard", icon: "🎨", }, { id: 8, title: "AI Prompt Engineering", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "hard", icon: "🤖", }, ]; const PROJECTS_DATA = [ { id: 1, title: "AI Chatbot Assistant", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "ai", image: "assets/images/projects/project1.jpg", url: "#", techStack: ["Python", "TensorFlow", "OpenAI"], }, { id: 2, title: "Security Audit Tool", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "cybersecurity", image: "assets/images/projects/project2.jpg", url: "#", techStack: ["Python", "Nmap", "Wireshark"], }, { id: 3, title: "Portfolio Website", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "ui", image: "assets/images/projects/project3.jpg", url: "#", techStack: ["HTML", "CSS", "JavaScript"], }, { id: 4, title: "Mobile App Design", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "ui", image: "assets/images/projects/project4.jpg", url: "#", techStack: ["Figma", "Adobe XD", "Sketch"], }, { id: 5, title: "Data Analysis Dashboard", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "other", image: "assets/images/projects/project5.jpg", url: "#", techStack: ["Python", "Pandas", "Matplotlib"], }, { id: 6, title: "E-commerce Platform", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "other", image: "assets/images/projects/project6.jpg", url: "#", techStack: ["React", "Node.js", "MongoDB"], }, ]; const CERTIFICATES_DATA = [ { id: 1, title: "AI Prompt Engineering Certification", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "ai", image: "assets/images/certificates/cert1.jpg", url: "#", techStack: ["OpenAI", "GPT", "Prompt Design"], }, { id: 2, title: "Certified Ethical Hacker", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "cybersecurity", image: "assets/images/certificates/cert2.jpg", url: "#", techStack: [ "Penetration Testing", "Network Security", "Vulnerability Assessment", ], }, { id: 3, title: "UI/UX Design Professional", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "ui", image: "assets/images/certificates/cert3.jpg", url: "#", techStack: ["Figma", "User Research", "Prototyping"], }, { id: 4, title: "Google Cloud Professional", description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.", category: "other", image: "assets/images/certificates/cert4.jpg", url: "#", techStack: ["Google Cloud", "DevOps", "Cloud Architecture"], }, ]; function filterData(data, category) { if (category === "all") { return data; } return data.filter((item) => item.category === category); } function paginateData(data, page, itemsPerPage) { const startIndex = (page - 1) * itemsPerPage; const endIndex = startIndex + itemsPerPage; return data.slice(0, endIndex); } function renderSkillCard(skill) { return ` <div class="card-base bg-light-bg dark:bg-dark-bg hover:shadow-card-hover fade-in" data-category="${skill.category}"> <div class="text-center"> <div class="text-4xl mb-4">${skill.icon}</div> <h3 class="font-heading font-heading-bold text-xl mb-3 text-light-text dark:text-dark-text">${skill.title}</h3> <p class="font-body text-light-text dark:text-dark-text">${skill.description}</p> </div> </div> `; } function renderProjectCard(project) { return ` <div class="card-base bg-light-bg dark:bg-dark-bg hover:shadow-card-hover fade-in" data-category="${project.category}"> <div class="mb-4 cursor-pointer" onclick="openPreview('project', ${project.id})"> <img src="${project.image}" alt="${project.title}" class="w-full h-48 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMzAgMTAwSDEwMFY3MEgxMzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDEwMEgxNDBWNzBIMTcwVjEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCA4MEgxMjBWNTBIMTUwVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"> </div> <h3 class="font-heading font-heading-bold text-xl mb-3 text-light-text dark:text-dark-text">${project.title}</h3> <p class="font-body text-light-text dark:text-dark-text mb-4">${project.description}</p> <a href="${project.url}" target="_blank" rel="noopener noreferrer" class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover inline-block"> Lihat </a> </div> `; } function renderCertificateCard(certificate) { return ` <div class="card-base bg-light-bg dark:bg-dark-bg hover:shadow-card-hover fade-in" data-category="${certificate.category}"> <div class="mb-4 cursor-pointer" onclick="openPreview('certificate', ${certificate.id})"> <img src="${certificate.image}" alt="${certificate.title}" class="w-full h-48 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMzAgMTAwSDEwMFY3MEgxMzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDEwMEgxNDBWNzBIMTcwVjEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCA4MEgxMjBWNTBIMTUwVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"> </div> <h3 class="font-heading font-heading-bold text-xl mb-3 text-light-text dark:text-dark-text">${certificate.title}</h3> <p class="font-body text-light-text dark:text-dark-text mb-4">${certificate.description}</p> <a href="${certificate.url}" target="_blank" rel="noopener noreferrer" class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover inline-block"> Lihat </a> </div> `; } function renderSkills() { const filteredData = filterData(SKILLS_DATA, STATE.currentSkillFilter); const paginatedData = paginateData( filteredData, STATE.skillsPage, CONFIG.ITEMS_PER_PAGE ); ELEMENTS.skillsGrid.innerHTML = paginatedData .map((skill) => renderSkillCard(skill)) .join(""); if (paginatedData.length < filteredData.length) { ELEMENTS.loadMoreSkills.classList.remove("hidden"); } else { ELEMENTS.loadMoreSkills.classList.add("hidden"); } setTimeout(() => { document.querySelectorAll("#skillsGrid .fade-in").forEach((el) => { el.classList.add("visible"); }); }, 100); } function renderProjects() { const filteredData = filterData(PROJECTS_DATA, STATE.currentProjectFilter); const paginatedData = paginateData( filteredData, STATE.projectsPage, CONFIG.ITEMS_PER_PAGE ); ELEMENTS.projectsGrid.innerHTML = paginatedData .map((project) => renderProjectCard(project)) .join(""); if (paginatedData.length < filteredData.length) { ELEMENTS.loadMoreProjects.classList.remove("hidden"); } else { ELEMENTS.loadMoreProjects.classList.add("hidden"); } setTimeout(() => { document.querySelectorAll("#projectsGrid .fade-in").forEach((el) => { el.classList.add("visible"); }); }, 100); } function renderCertificates() { const filteredData = filterData( CERTIFICATES_DATA, STATE.currentCertificateFilter ); const paginatedData = paginateData( filteredData, STATE.certificatesPage, CONFIG.ITEMS_PER_PAGE ); ELEMENTS.certificatesGrid.innerHTML = paginatedData .map((certificate) => renderCertificateCard(certificate)) .join(""); if (paginatedData.length < filteredData.length) { ELEMENTS.loadMoreCertificates.classList.remove("hidden"); } else { ELEMENTS.loadMoreCertificates.classList.add("hidden"); } setTimeout(() => { document.querySelectorAll("#certificatesGrid .fade-in").forEach((el) => { el.classList.add("visible"); }); }, 100); } function openPreview(type, id) { let data, item; if (type === "project") { data = PROJECTS_DATA; } else if (type === "certificate") { data = CERTIFICATES_DATA; } item = data.find((i) => i.id === id); if (!item) return; ELEMENTS.previewTitle.textContent = item.title; ELEMENTS.previewContent.innerHTML = ` <div class="mb-6"> <img src="${item.image}" alt="${item.title}" class="w-full h-64 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMzAgMTAwSDEwMFY3MEgxMzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDEwMEgxNDBWNzBIMTcwVjEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCA4MEgxMjBWNTBIMTUwVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'"> </div> <p class="font-body text-lg mb-6 text-light-text dark:text-dark-text">${item.description}</p> <div class="mb-6"> <h4 class="font-heading font-heading-bold text-lg mb-3 text-light-text dark:text-dark-text">Tech Stack:</h4> <div class="flex flex-wrap gap-2"> ${item.techStack .map( (tech) => ` <button class="btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border text-sm"> ${tech} </button> ` ) .join("")} </div> </div> <a href="${item.url}" target="_blank" rel="noopener noreferrer" class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover"> Lihat ${type === "project" ? "Proyek" : "Sertifikat"} </a> `; ELEMENTS.previewModal.classList.add("active"); } function closePreview() { ELEMENTS.previewModal.classList.remove("active"); } function handleContactForm(e) { e.preventDefault(); const formData = new FormData(e.target); const name = formData.get("name"); const email = formData.get("email"); const message = formData.get("message"); if (!name || !email || !message) { showNotification("Semua field harus diisi!", "error"); return; } showLoading(); setTimeout(() => { hideLoading(); showNotification( "Pesan sudah terkirim. Terimakasih atas masukkan anda", "success" ); e.target.reset(); }, 2000); } function handleSkillFilter(filter) { STATE.currentSkillFilter = filter; STATE.skillsPage = 1; ELEMENTS.skillFilters.forEach((btn) => { btn.classList.remove( "bg-light-text", "dark:bg-dark-text", "text-light-bg", "dark:text-dark-bg" ); btn.classList.add( "bg-light-card", "dark:bg-dark-card", "text-light-text", "dark:text-dark-text", "border", "border-light-border", "dark:border-dark-border" ); }); const activeBtn = document.querySelector(`[data-filter="${filter}"]`); if (activeBtn) { activeBtn.classList.remove( "bg-light-card", "dark:bg-dark-card", "text-light-text", "dark:text-dark-text", "border", "border-light-border", "dark:border-dark-border" ); activeBtn.classList.add( "bg-light-text", "dark:bg-dark-text", "text-light-bg", "dark:text-dark-bg" ); } renderSkills(); } function handleProjectFilter(filter) { STATE.currentProjectFilter = filter; STATE.projectsPage = 1; ELEMENTS.projectFilters.forEach((btn) => { btn.classList.remove( "bg-light-text", "dark:bg-dark-text", "text-light-bg", "dark:text-dark-bg" ); btn.classList.add( "bg-light-card", "dark:bg-dark-card", "text-light-text", "dark:text-dark-text", "border", "border-light-border", "dark:border-dark-border" ); }); const activeBtn = document.querySelector(`[data-filter="${filter}"]`); if (activeBtn) { activeBtn.classList.remove( "bg-light-card", "dark:bg-dark-card", "text-light-text", "dark:text-dark-text", "border", "border-light-border", "dark:border-dark-border" ); activeBtn.classList.add( "bg-light-text", "dark:bg-dark-text", "text-light-bg", "dark:text-dark-bg" ); } renderProjects(); } function handleCertificateFilter(filter) { STATE.currentCertificateFilter = filter; STATE.certificatesPage = 1; ELEMENTS.certificateFilters.forEach((btn) => { btn.classList.remove( "bg-light-text", "dark:bg-dark-text", "text-light-bg", "dark:text-dark-bg" ); btn.classList.add( "bg-light-card", "dark:bg-dark-card", "text-light-text", "dark:text-dark-text", "border", "border-light-border", "dark:border-dark-border" ); }); const activeBtn = document.querySelector(`[data-filter="${filter}"]`); if (activeBtn) { activeBtn.classList.remove( "bg-light-card", "dark:bg-dark-card", "text-light-text", "dark:text-dark-text", "border", "border-light-border", "dark:border-dark-border" ); activeBtn.classList.add( "bg-light-text", "dark:bg-dark-text", "text-light-bg", "dark:text-dark-bg" ); } renderCertificates(); } function initializeElements() { ELEMENTS.navbar = document.getElementById("navbar"); ELEMENTS.brandName = document.getElementById("brandName"); ELEMENTS.mobileMenuBtn = document.getElementById("mobileMenuBtn"); ELEMENTS.mobileMenu = document.getElementById("mobileMenu"); ELEMENTS.themeToggleDesktop = document.getElementById("themeToggleDesktop"); ELEMENTS.themeToggleMobile = document.getElementById("themeToggleMobile"); ELEMENTS.sunIcon = document.getElementById("sunIcon"); ELEMENTS.moonIcon = document.getElementById("moonIcon"); ELEMENTS.backToTop = document.getElementById("backToTop"); ELEMENTS.loadingOverlay = document.getElementById("loadingOverlay"); ELEMENTS.skillsGrid = document.getElementById("skillsGrid"); ELEMENTS.projectsGrid = document.getElementById("projectsGrid"); ELEMENTS.certificatesGrid = document.getElementById("certificatesGrid"); ELEMENTS.loadMoreSkills = document.getElementById("loadMoreSkills"); ELEMENTS.loadMoreProjects = document.getElementById("loadMoreProjects"); ELEMENTS.loadMoreCertificates = document.getElementById( "loadMoreCertificates" ); ELEMENTS.skillFilters = document.querySelectorAll(".skill-filter"); ELEMENTS.projectFilters = document.querySelectorAll(".project-filter"); ELEMENTS.certificateFilters = document.querySelectorAll( ".certificate-filter" ); ELEMENTS.contactForm = document.getElementById("contactForm"); ELEMENTS.previewModal = document.getElementById("previewModal"); ELEMENTS.closePreview = document.getElementById("closePreview"); ELEMENTS.previewTitle = document.getElementById("previewTitle"); ELEMENTS.previewContent = document.getElementById("previewContent"); ELEMENTS.notificationContainer = document.getElementById( "notificationContainer" ); } function setupEventListeners() { ELEMENTS.themeToggleDesktop.addEventListener("click", toggleTheme); ELEMENTS.themeToggleMobile.addEventListener("change", toggleTheme); ELEMENTS.brandName.addEventListener("click", handleBrandClick); ELEMENTS.mobileMenuBtn.addEventListener("click", toggleMobileMenu); ELEMENTS.backToTop.addEventListener("click", () => { window.scrollTo({ top: 0, behavior: "smooth" }); }); document.querySelectorAll('a[href^="#"]').forEach((link) => { link.addEventListener("click", handleNavigation); }); window.addEventListener("scroll", throttle(updateNavbarOnScroll, 16)); ELEMENTS.skillFilters.forEach((btn) => { btn.addEventListener("click", (e) => { handleSkillFilter(e.target.dataset.filter); }); }); ELEMENTS.projectFilters.forEach((btn) => { btn.addEventListener("click", (e) => { handleProjectFilter(e.target.dataset.filter); }); }); ELEMENTS.certificateFilters.forEach((btn) => { btn.addEventListener("click", (e) => { handleCertificateFilter(e.target.dataset.filter); }); }); ELEMENTS.loadMoreSkills.addEventListener("click", () => { STATE.skillsPage++; renderSkills(); }); ELEMENTS.loadMoreProjects.addEventListener("click", () => { STATE.projectsPage++; renderProjects(); }); ELEMENTS.loadMoreCertificates.addEventListener("click", () => { STATE.certificatesPage++; renderCertificates(); }); ELEMENTS.contactForm.addEventListener("submit", handleContactForm); ELEMENTS.closePreview.addEventListener("click", closePreview); ELEMENTS.previewModal.addEventListener("click", (e) => { if (e.target === ELEMENTS.previewModal) { closePreview(); } }); const observerOptions = { threshold: 0.1, rootMargin: "0px 0px -50px 0px", }; const observer = new IntersectionObserver((entries) => { entries.forEach((entry) => { if (entry.isIntersecting) { entry.target.classList.add("visible"); } }); }, observerOptions); document.querySelectorAll(".fade-in").forEach((el) => { observer.observe(el); }); } function initializeApp() { initializeElements(); initializeTheme(); setupEventListeners(); disableRightClick(); disableDevTools(); disableCopyPaste(); renderSkills(); renderProjects(); renderCertificates(); setTimeout(() => { hideLoading(); }, 500); } document.addEventListener("DOMContentLoaded", initializeApp); window.openPreview = openPreview; window.closePreview = closePreview;