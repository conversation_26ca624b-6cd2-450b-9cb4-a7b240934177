<!doctype html>
<html lang="id" class="smooth-scroll">
  <head>
    <!-- Meta Tags untuk SEO dan Accessibility -->
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Personal Portfolio Muhammad <PERSON> - AI Prompt Engineer, Cyber Security, UI Designer"
    />
    <meta
      name="keywords"
      content="<PERSON>, AI Prompt Engineer, Cyber Security, UI Designer, Portfolio"
    />
    <meta name="author" content="<PERSON>" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph Meta Tags untuk Social Media -->
    <meta
      property="og:title"
      content="Muhammad <PERSON> - Personal Portfolio"
    />
    <meta
      property="og:description"
      content="AI Prompt Engineer | Cyber Security | UI Designer"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="" />
    <meta
      property="og:image"
      content="assets/images/profile/muhammadsyaamilmuzhaffar.png"
    />

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Muhammad Syaamil Muzhaffar - Personal Portfolio"
    />
    <meta
      name="twitter:description"
      content="AI Prompt Engineer | Cyber Security | UI Designer"
    />
    <meta
      name="twitter:image"
      content="assets/images/profile/muhammadsyaamilmuzhaffar.png"
    />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/icons/ui/favicon.ico" />

    <!-- Preload Critical Resources -->
    <link rel="preload" href="dist/css/output.css" as="style" />
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700&family=Open+Sans:wght@300;400;500;600;700&display=swap"
      as="style"
    />

    <!-- Stylesheets -->
    <link href="dist/css/output.css" rel="stylesheet" />

    <!-- Title -->
    <title>Muhammad Syaamil Muzhaffar - Personal Portfolio</title>
  </head>

  <body
    class="font-body bg-light-bg dark:bg-dark-bg text-light-text dark:text-dark-text transition-colors duration-300 no-context"
  >
    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
      <div class="spinner"></div>
    </div>

    <!-- Navigation Bar -->
    <nav
      id="navbar"
      class="fixed top-0 left-0 right-0 z-navbar bg-light-bg dark:bg-dark-bg transition-all duration-300 border-b border-light-border dark:border-dark-border"
    >
      <div class="container mx-auto px-6 lg:px-12">
        <div class="flex items-center justify-between h-16">
          <!-- Logo/Brand Name -->
          <div class="flex-shrink-0">
            <button
              id="brandName"
              class="font-heading font-heading-bold text-xl text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 no-select"
            >
              Muhammad Syaamil Muzhaffar
            </button>
          </div>

          <!-- Desktop Navigation Menu -->
          <div class="hidden lg:block">
            <div class="ml-10 flex items-baseline space-x-8">
              <a
                href="#about"
                class="nav-link font-body text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 px-3 py-2"
                >Tentang Saya</a
              >
              <a
                href="#skills"
                class="nav-link font-body text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 px-3 py-2"
                >Skill</a
              >
              <a
                href="#projects"
                class="nav-link font-body text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 px-3 py-2"
                >Proyek</a
              >
              <a
                href="#certificates"
                class="nav-link font-body text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 px-3 py-2"
                >Sertifikat</a
              >
              <a
                href="#techstack"
                class="nav-link font-body text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 px-3 py-2"
                >Tech Stack</a
              >
              <a
                href="#contact"
                class="nav-link font-body text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300 px-3 py-2"
                >Kontak</a
              >
            </div>
          </div>

          <!-- Desktop Dark Mode Toggle (Sun/Moon) -->
          <div class="hidden lg:flex items-center space-x-4">
            <button
              id="themeToggleDesktop"
              class="p-2 rounded-full bg-light-card dark:bg-dark-card hover:bg-accent-gray hover:dark:bg-accent-gray-hover transition-all duration-300"
              aria-label="Toggle Dark Mode"
            >
              <svg
                id="sunIcon"
                class="w-5 h-5 text-light-text dark:text-dark-text"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fill-rule="evenodd"
                  d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
                  clip-rule="evenodd"
                ></path>
              </svg>
              <svg
                id="moonIcon"
                class="w-5 h-5 text-light-text dark:text-dark-text hidden"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"
                ></path>
              </svg>
            </button>
          </div>

          <!-- Mobile Menu Button -->
          <div class="lg:hidden flex items-center space-x-4">
            <!-- Mobile Dark Mode Toggle (Switch) -->
            <label class="relative inline-flex items-center cursor-pointer">
              <input type="checkbox" id="themeToggleMobile" class="sr-only" />
              <div
                class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"
              ></div>
            </label>

            <!-- Hamburger Menu Button -->
            <button
              id="mobileMenuBtn"
              class="p-2 rounded-md text-light-text dark:text-dark-text hover:text-accent-gray-hover focus:outline-none"
              aria-label="Toggle Mobile Menu"
            >
              <div class="w-6 h-6 flex flex-col justify-center items-center">
                <span class="hamburger-line w-6 h-0.5 bg-current mb-1"></span>
                <span class="hamburger-line w-6 h-0.5 bg-current mb-1"></span>
                <span class="hamburger-line w-6 h-0.5 bg-current"></span>
              </div>
            </button>
          </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobileMenu" class="lg:hidden hidden">
          <div
            class="px-2 pt-2 pb-3 space-y-1 bg-light-card dark:bg-dark-card rounded-lg mt-2 shadow-card"
          >
            <a
              href="#about"
              class="nav-link block px-3 py-2 text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
              >Tentang Saya</a
            >
            <a
              href="#skills"
              class="nav-link block px-3 py-2 text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
              >Skill</a
            >
            <a
              href="#projects"
              class="nav-link block px-3 py-2 text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
              >Proyek</a
            >
            <a
              href="#certificates"
              class="nav-link block px-3 py-2 text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
              >Sertifikat</a
            >
            <a
              href="#techstack"
              class="nav-link block px-3 py-2 text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
              >Tech Stack</a
            >
            <a
              href="#contact"
              class="nav-link block px-3 py-2 text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
              >Kontak</a
            >
          </div>
        </div>
      </div>
    </nav>

    <!-- Back to Top Button -->
    <button
      id="backToTop"
      class="fixed bottom-6 right-6 z-50 bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg p-3 rounded-full shadow-button hover:shadow-button-hover transition-all duration-300 opacity-0 invisible"
    >
      <svg
        class="w-6 h-6"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          stroke-linecap="round"
          stroke-linejoin="round"
          stroke-width="2"
          d="M5 10l7-7m0 0l7 7m-7-7v18"
        ></path>
      </svg>
    </button>

    <!-- Main Content -->
    <main class="pt-16">
      <!-- Hero Section -->
      <section
        id="hero"
        class="min-h-screen flex items-center justify-center px-6 lg:px-12"
      >
        <div class="container mx-auto">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- Left Content -->
            <div class="text-center lg:text-left fade-in">
              <p
                class="font-body text-lg mb-4 text-light-text dark:text-dark-text"
              >
                Halo Semua 👋, Saya
              </p>
              <h1
                class="font-heading font-heading-bold text-4xl lg:text-6xl mb-6 text-light-text dark:text-dark-text"
              >
                Muhammad Syaamil Muzhaffar
              </h1>
              <h2 class="font-body text-xl lg:text-2xl mb-6 text-accent-gray">
                AI Prompt Engineer | Cyber Security | UI Designer
              </h2>
              <p
                class="font-body text-lg mb-8 text-light-text dark:text-dark-text"
              >
                Coba dulu, coba dulu
              </p>
              <a
                href="#contact"
                class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover inline-block"
              >
                Hubungi saya
              </a>
            </div>

            <!-- Right Content - Profile Image -->
            <div class="text-center fade-in">
              <div class="relative inline-block">
                <img
                  src="assets/images/profile/muhammadsyaamilmuzhaffar.png"
                  alt="Muhammad Syaamil Muzhaffar Profile Picture"
                  class="w-80 h-80 lg:w-96 lg:h-96 rounded-full object-cover shadow-card hover:shadow-card-hover transition-all duration-300"
                  loading="lazy"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- About Section -->
      <section id="about" class="py-20 px-6 lg:px-12">
        <div class="container mx-auto">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            <!-- Left Content -->
            <div class="fade-in">
              <h2
                class="font-heading font-heading-bold text-3xl lg:text-4xl mb-6 text-light-text dark:text-dark-text"
              >
                Tentang Saya
              </h2>
              <h3
                class="font-heading font-heading-bold text-xl lg:text-2xl mb-6 text-light-text dark:text-dark-text"
              >
                Siapa saya?
              </h3>
              <p
                class="font-body text-lg leading-relaxed text-light-text dark:text-dark-text"
              >
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
                enim ad minim veniam, quis nostrud exercitation ullamco laboris
                nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor
                in reprehenderit in voluptate velit esse cillum dolore eu fugiat
                nulla pariatur. Excepteur sint occaecat cupidatat non proident,
                sunt in culpa qui officia deserunt mollit anim id est laborum.
              </p>
            </div>

            <!-- Right Content -->
            <div class="fade-in">
              <h3
                class="font-heading font-heading-bold text-xl lg:text-2xl mb-6 text-light-text dark:text-dark-text"
              >
                Ingin mengenal saya lebih jauh?
              </h3>
              <p
                class="font-body text-lg mb-8 text-light-text dark:text-dark-text"
              >
                Kunjungi sosial media saya.
              </p>

              <!-- Social Media Icons -->
              <div class="flex flex-wrap gap-4">
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="Credly"
                >
                  <img
                    src="assets/icons/social-media/credly.svg"
                    alt="Credly"
                    class="w-6 h-6"
                  />
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="GitHub"
                >
                  <img
                    src="assets/icons/social-media/github.svg"
                    alt="GitHub"
                    class="w-6 h-6"
                  />
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="Instagram"
                >
                  <img
                    src="assets/icons/social-media/instagram.svg"
                    alt="Instagram"
                    class="w-6 h-6"
                  />
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="LinkedIn"
                >
                  <img
                    src="assets/icons/social-media/linkedin.svg"
                    alt="LinkedIn"
                    class="w-6 h-6"
                  />
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="Medium"
                >
                  <img
                    src="assets/icons/social-media/medium.svg"
                    alt="Medium"
                    class="w-6 h-6"
                  />
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="Spotify"
                >
                  <img
                    src="assets/icons/social-media/spotify.svg"
                    alt="Spotify"
                    class="w-6 h-6"
                  />
                </a>
                <a
                  href="#"
                  target="_blank"
                  rel="noopener noreferrer"
                  class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                  aria-label="Twitter"
                >
                  <img
                    src="assets/icons/social-media/twitter.svg"
                    alt="Twitter"
                    class="w-6 h-6"
                  />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Skills Section -->
      <section
        id="skills"
        class="py-20 px-6 lg:px-12 bg-light-card dark:bg-dark-card"
      >
        <div class="container mx-auto">
          <!-- Section Header -->
          <div class="text-center mb-12 fade-in">
            <h2
              class="font-heading font-heading-bold text-3xl lg:text-4xl mb-4 text-light-text dark:text-dark-text"
            >
              Skill
            </h2>
            <h3
              class="font-heading font-heading-bold text-xl lg:text-2xl mb-4 text-light-text dark:text-dark-text"
            >
              Keahlian
            </h3>
            <p class="font-body text-lg text-light-text dark:text-dark-text">
              Kemampuan yang saya miliki
            </p>
          </div>

          <!-- Filter Buttons -->
          <div class="flex justify-center mb-12 fade-in">
            <div class="flex flex-wrap gap-4">
              <button
                class="skill-filter btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover"
                data-filter="all"
              >
                Semua
              </button>
              <button
                class="skill-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="soft"
              >
                Soft Skill
              </button>
              <button
                class="skill-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="hard"
              >
                Hard Skill
              </button>
            </div>
          </div>

          <!-- Skills Grid -->
          <div
            id="skillsGrid"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            <!-- Skill cards akan di-generate oleh JavaScript -->
          </div>

          <!-- Load More Button -->
          <div class="text-center mt-12 fade-in">
            <button
              id="loadMoreSkills"
              class="btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border hidden"
            >
              Lihat selengkapnya
            </button>
          </div>
        </div>
      </section>

      <!-- Projects Section -->
      <section id="projects" class="py-20 px-6 lg:px-12">
        <div class="container mx-auto">
          <!-- Section Header -->
          <div class="text-center mb-12 fade-in">
            <h2
              class="font-heading font-heading-bold text-3xl lg:text-4xl mb-4 text-light-text dark:text-dark-text"
            >
              Proyek
            </h2>
            <h3
              class="font-heading font-heading-bold text-xl lg:text-2xl mb-4 text-light-text dark:text-dark-text"
            >
              Proyek Terbaru
            </h3>
            <p class="font-body text-lg text-light-text dark:text-dark-text">
              Proyek yang sudah saya kerjakan
            </p>
          </div>

          <!-- Filter Buttons -->
          <div class="flex justify-center mb-12 fade-in">
            <div class="flex flex-wrap gap-4">
              <button
                class="project-filter btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover"
                data-filter="all"
              >
                Semua
              </button>
              <button
                class="project-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="ai"
              >
                AI Prompt Engineer
              </button>
              <button
                class="project-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="cybersecurity"
              >
                Cyber Security
              </button>
              <button
                class="project-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="ui"
              >
                UI Designer
              </button>
              <button
                class="project-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="other"
              >
                Lainnya
              </button>
            </div>
          </div>

          <!-- Projects Grid -->
          <div
            id="projectsGrid"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            <!-- Project cards akan di-generate oleh JavaScript -->
          </div>

          <!-- Load More Button -->
          <div class="text-center mt-12 fade-in">
            <button
              id="loadMoreProjects"
              class="btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border hidden"
            >
              Lihat selengkapnya
            </button>
          </div>
        </div>
      </section>

      <!-- Certificates Section -->
      <section
        id="certificates"
        class="py-20 px-6 lg:px-12 bg-light-card dark:bg-dark-card"
      >
        <div class="container mx-auto">
          <!-- Section Header -->
          <div class="text-center mb-12 fade-in">
            <h2
              class="font-heading font-heading-bold text-3xl lg:text-4xl mb-4 text-light-text dark:text-dark-text"
            >
              Sertifikat
            </h2>
            <h3
              class="font-heading font-heading-bold text-xl lg:text-2xl mb-4 text-light-text dark:text-dark-text"
            >
              Sertifikat Terbaru
            </h3>
            <p class="font-body text-lg text-light-text dark:text-dark-text">
              Pencapaian yang sudah diraih
            </p>
          </div>

          <!-- Filter Buttons -->
          <div class="flex justify-center mb-12 fade-in">
            <div class="flex flex-wrap gap-4">
              <button
                class="certificate-filter btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover"
                data-filter="all"
              >
                Semua
              </button>
              <button
                class="certificate-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="ai"
              >
                AI Prompt Engineer
              </button>
              <button
                class="certificate-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="cybersecurity"
              >
                Cyber Security
              </button>
              <button
                class="certificate-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="ui"
              >
                UI Designer
              </button>
              <button
                class="certificate-filter btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border"
                data-filter="other"
              >
                Lainnya
              </button>
            </div>
          </div>

          <!-- Certificates Grid -->
          <div
            id="certificatesGrid"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            <!-- Certificate cards akan di-generate oleh JavaScript -->
          </div>

          <!-- Load More Button -->
          <div class="text-center mt-12 fade-in">
            <button
              id="loadMoreCertificates"
              class="btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border hidden"
            >
              Lihat selengkapnya
            </button>
          </div>
        </div>
      </section>

      <!-- Tech Stack Section -->
      <section id="techstack" class="py-20 px-6 lg:px-12">
        <div class="container mx-auto">
          <!-- Section Header -->
          <div class="text-center mb-12 fade-in">
            <h2
              class="font-heading font-heading-bold text-3xl lg:text-4xl mb-4 text-light-text dark:text-dark-text"
            >
              Tech Stack
            </h2>
            <h3
              class="font-heading font-heading-bold text-xl lg:text-2xl mb-4 text-light-text dark:text-dark-text"
            >
              Teknologi yang Digunakan
            </h3>
            <p class="font-body text-lg text-light-text dark:text-dark-text">
              Teknologi yang saya pakai dalam pembuatan personal proyek
            </p>
          </div>

          <!-- Tech Stack Icons -->
          <div class="flex flex-wrap justify-center gap-8 fade-in">
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="CSS3"
            >
              <img
                src="assets/icons/tech-stacks/css3.svg"
                alt="CSS3"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="Git"
            >
              <img
                src="assets/icons/tech-stacks/git.svg"
                alt="Git"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="GitHub Pages"
            >
              <img
                src="assets/icons/tech-stacks/githubpages.svg"
                alt="GitHub Pages"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="HTML5"
            >
              <img
                src="assets/icons/tech-stacks/html5.svg"
                alt="HTML5"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="JavaScript"
            >
              <img
                src="assets/icons/tech-stacks/javascript.svg"
                alt="JavaScript"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="Node.js"
            >
              <img
                src="assets/icons/tech-stacks/nodejs.svg"
                alt="Node.js"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
            <a
              href="#"
              target="_blank"
              rel="noopener noreferrer"
              class="tech-icon group"
              aria-label="Tailwind CSS"
            >
              <img
                src="assets/icons/tech-stacks/tailwindcss.svg"
                alt="Tailwind CSS"
                class="w-16 h-16 transition-all duration-300 filter grayscale group-hover:grayscale-0 group-hover:scale-110"
              />
            </a>
          </div>
        </div>
      </section>

      <!-- Contact Section -->
      <section
        id="contact"
        class="py-20 px-6 lg:px-12 bg-light-card dark:bg-dark-card"
      >
        <div class="container mx-auto">
          <!-- Section Header -->
          <div class="text-center mb-12 fade-in">
            <h2
              class="font-heading font-heading-bold text-3xl lg:text-4xl mb-4 text-light-text dark:text-dark-text"
            >
              Kontak
            </h2>
            <h3
              class="font-heading font-heading-bold text-xl lg:text-2xl mb-4 text-light-text dark:text-dark-text"
            >
              Hubungi Saya
            </h3>
            <p class="font-body text-lg text-light-text dark:text-dark-text">
              Silakan isi form di bawah ini untuk menghubungi Saya
            </p>
          </div>

          <!-- Contact Form -->
          <div class="max-w-2xl mx-auto fade-in">
            <form id="contactForm" class="space-y-6">
              <!-- Nama Field -->
              <div>
                <label
                  for="name"
                  class="block font-body font-medium text-light-text dark:text-dark-text mb-2"
                >
                  Nama
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  required
                  class="w-full px-4 py-3 border border-light-border dark:border-dark-border rounded-button bg-light-bg dark:bg-dark-bg text-light-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-accent-gray transition-all duration-300"
                  placeholder="Masukkan nama Anda"
                />
              </div>

              <!-- Email Field -->
              <div>
                <label
                  for="email"
                  class="block font-body font-medium text-light-text dark:text-dark-text mb-2"
                >
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  required
                  class="w-full px-4 py-3 border border-light-border dark:border-dark-border rounded-button bg-light-bg dark:bg-dark-bg text-light-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-accent-gray transition-all duration-300"
                  placeholder="Masukkan email Anda"
                />
              </div>

              <!-- Pesan Field -->
              <div>
                <label
                  for="message"
                  class="block font-body font-medium text-light-text dark:text-dark-text mb-2"
                >
                  Pesan
                </label>
                <textarea
                  id="message"
                  name="message"
                  rows="6"
                  required
                  class="w-full px-4 py-3 border border-light-border dark:border-dark-border rounded-button bg-light-bg dark:bg-dark-bg text-light-text dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-accent-gray transition-all duration-300 resize-none"
                  placeholder="Tulis pesan Anda di sini"
                ></textarea>
              </div>

              <!-- Submit Button -->
              <div class="text-center">
                <button
                  type="submit"
                  class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover"
                >
                  Kirim
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>
    </main>

    <!-- Footer -->
    <footer
      class="py-12 px-6 lg:px-12 bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg"
    >
      <div class="container mx-auto">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Contact Info -->
          <div class="fade-in">
            <h3 class="font-heading font-heading-bold text-xl mb-4">
              Hubungi Saya
            </h3>
            <p class="font-body text-lg"><EMAIL></p>
          </div>

          <!-- Navigation Links -->
          <div class="fade-in">
            <h3 class="font-heading font-heading-bold text-xl mb-4">
              Cari apa?
            </h3>
            <div class="space-y-2">
              <a
                href="#about"
                class="block font-body hover:text-accent-gray transition-colors duration-300"
                >Tentang Saya</a
              >
              <a
                href="#skills"
                class="block font-body hover:text-accent-gray transition-colors duration-300"
                >Skill</a
              >
              <a
                href="#projects"
                class="block font-body hover:text-accent-gray transition-colors duration-300"
                >Proyek</a
              >
              <a
                href="#certificates"
                class="block font-body hover:text-accent-gray transition-colors duration-300"
                >Sertifikat</a
              >
              <a
                href="#techstack"
                class="block font-body hover:text-accent-gray transition-colors duration-300"
                >Tech Stack</a
              >
              <a
                href="#contact"
                class="block font-body hover:text-accent-gray transition-colors duration-300"
                >Kontak</a
              >
            </div>
          </div>

          <!-- Social Media & Copyright -->
          <div class="fade-in">
            <!-- Social Media Icons -->
            <div class="flex flex-wrap gap-4 mb-6">
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="Credly"
              >
                <img
                  src="assets/icons/social-media/credly.svg"
                  alt="Credly"
                  class="w-6 h-6"
                />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="GitHub"
              >
                <img
                  src="assets/icons/social-media/github.svg"
                  alt="GitHub"
                  class="w-6 h-6"
                />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="Instagram"
              >
                <img
                  src="assets/icons/social-media/instagram.svg"
                  alt="Instagram"
                  class="w-6 h-6"
                />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="LinkedIn"
              >
                <img
                  src="assets/icons/social-media/linkedin.svg"
                  alt="LinkedIn"
                  class="w-6 h-6"
                />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="Medium"
              >
                <img
                  src="assets/icons/social-media/medium.svg"
                  alt="Medium"
                  class="w-6 h-6"
                />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="Spotify"
              >
                <img
                  src="assets/icons/social-media/spotify.svg"
                  alt="Spotify"
                  class="w-6 h-6"
                />
              </a>
              <a
                href="#"
                target="_blank"
                rel="noopener noreferrer"
                class="icon-social bg-accent-gray hover:bg-accent-gray-hover flex items-center justify-center"
                aria-label="Twitter"
              >
                <img
                  src="assets/icons/social-media/twitter.svg"
                  alt="Twitter"
                  class="w-6 h-6"
                />
              </a>
            </div>

            <!-- Copyright -->
            <p class="font-body text-sm">
              Copyright © 2025 Muhammad Syaamil Muzhaffar
            </p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Preview Modal -->
    <div id="previewModal" class="preview-modal">
      <div class="preview-content">
        <div class="flex justify-between items-center mb-6">
          <h3
            id="previewTitle"
            class="font-heading font-heading-bold text-2xl text-light-text dark:text-dark-text"
          ></h3>
          <button
            id="closePreview"
            class="text-light-text dark:text-dark-text hover:text-accent-gray-hover transition-colors duration-300"
          >
            <svg
              class="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              ></path>
            </svg>
          </button>
        </div>
        <div id="previewContent">
          <!-- Preview content akan di-generate oleh JavaScript -->
        </div>
      </div>
    </div>

    <!-- Notification Container -->
    <div
      id="notificationContainer"
      class="fixed top-20 right-6 z-notification space-y-4"
    >
      <!-- Notifications akan di-generate oleh JavaScript -->
    </div>

    <!-- JavaScript -->
    <script src="dist/js/script.min.js"></script>
  </body>
</html>
