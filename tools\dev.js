/**
 * Development Script
 * <PERSON>ar Personal Portfolio
 */

const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🚀 Starting development server...");

// Start TailwindCSS watch mode
console.log("👀 Starting TailwindCSS watch mode...");
const tailwindProcess = spawn(
  "npx",
  [
    "tailwindcss",
    "-i",
    "./src/css/input.css",
    "-o",
    "./dist/css/output.css",
    "--watch",
  ],
  {
    stdio: "inherit",
    shell: true,
  }
);

// Handle process termination
process.on("SIGINT", () => {
  console.log("\n🛑 Stopping development server...");
  tailwindProcess.kill();
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\n🛑 Stopping development server...");
  tailwindProcess.kill();
  process.exit(0);
});

console.log("✅ Development server started!");
console.log("📁 Open index.html in your browser");
console.log("🔄 TailwindCSS is watching for changes...");
console.log("⏹️  Press Ctrl+C to stop");
