/**
 * Build Script untuk Production
 * <PERSON> Personal Portfolio
 */

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

console.log("🚀 Starting production build...");

try {
  // Build TailwindCSS dengan minifikasi
  console.log("📦 Building TailwindCSS...");
  execSync(
    "npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --minify",
    {
      stdio: "inherit",
    }
  );

  // Minify JavaScript (sederhana)
  console.log("🗜️ Minifying JavaScript...");
  const jsContent = fs.readFileSync("./dist/js/script.js", "utf8");

  // Remove comments dan extra whitespace
  const minifiedJs = jsContent
    .replace(/\/\*[\s\S]*?\*\//g, "") // Remove block comments
    .replace(/\/\/.*$/gm, "") // Remove line comments
    .replace(/\s+/g, " ") // Replace multiple spaces with single space
    .trim();

  fs.writeFileSync("./dist/js/script.min.js", minifiedJs);

  // Update HTML untuk production
  console.log("📝 Updating HTML for production...");
  let htmlContent = fs.readFileSync("./index.html", "utf8");
  htmlContent = htmlContent.replace(
    '<script src="dist/js/script.js"></script>',
    '<script src="dist/js/script.min.js"></script>'
  );

  fs.writeFileSync("./index.prod.html", htmlContent);

  console.log("✅ Production build completed successfully!");
  console.log("📁 Files generated:");
  console.log("   - dist/css/output.css (minified)");
  console.log("   - dist/js/script.min.js");
  console.log("   - index.prod.html");
} catch (error) {
  console.error("❌ Build failed:", error.message);
  process.exit(1);
}
