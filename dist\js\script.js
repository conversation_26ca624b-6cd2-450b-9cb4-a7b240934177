/**
 * Personal Portfolio JavaScript
 * Muhammad <PERSON>
 *
 * Fitur utama:
 * - Dark/Light Mode Toggle
 * - Hamburger Menu Navigation
 * - Smooth Scrolling
 * - Security Features (Disable Right Click, Inspect, Copy-Paste)
 * - Dynamic Content Loading
 * - Form Handling
 * - Animations & Interactions
 */

// ==========================================
// GLOBAL VARIABLES & CONFIGURATION
// ==========================================

// Konfigurasi aplikasi
const CONFIG = {
  ANIMATION_DURATION: 300,
  NOTIFICATION_DURATION: 3000,
  ITEMS_PER_PAGE: 6,
  SCROLL_THRESHOLD: 100,
};

// State management
const STATE = {
  isDarkMode: false,
  isMobileMenuOpen: false,
  currentSkillFilter: "all",
  currentProjectFilter: "all",
  currentCertificateFilter: "all",
  skillsPage: 1,
  projectsPage: 1,
  certificatesPage: 1,
};

// DOM Elements Cache
const ELEMENTS = {
  // Navigation
  navbar: null,
  brandName: null,
  mobileMenuBtn: null,
  mobileMenu: null,

  // Theme Toggle
  themeToggleDesktop: null,
  themeToggleMobile: null,
  sunIcon: null,
  moonIcon: null,

  // Back to Top
  backToTop: null,

  // Loading
  loadingOverlay: null,

  // Grids
  skillsGrid: null,
  projectsGrid: null,
  certificatesGrid: null,

  // Load More Buttons
  loadMoreSkills: null,
  loadMoreProjects: null,
  loadMoreCertificates: null,

  // Filter Buttons
  skillFilters: null,
  projectFilters: null,
  certificateFilters: null,

  // Forms
  contactForm: null,

  // Modals
  previewModal: null,
  closePreview: null,
  previewTitle: null,
  previewContent: null,

  // Notifications
  notificationContainer: null,
};

// ==========================================
// UTILITY FUNCTIONS
// ==========================================

/**
 * Debounce function untuk optimasi performance
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Throttle function untuk scroll events
 */
function throttle(func, limit) {
  let inThrottle;
  return function () {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

/**
 * Smooth scroll ke element tertentu
 */
function smoothScrollTo(element) {
  if (element) {
    const offsetTop = element.offsetTop - 80; // Offset untuk navbar
    window.scrollTo({
      top: offsetTop,
      behavior: "smooth",
    });
  }
}

/**
 * Generate unique ID
 */
function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// ==========================================
// SECURITY FEATURES
// ==========================================

/**
 * Disable right click context menu
 */
function disableRightClick() {
  document.addEventListener("contextmenu", function (e) {
    e.preventDefault();
    showNotification("Klik kanan tidak diizinkan!", "error");
    return false;
  });
}

/**
 * Disable F12 dan shortcut developer tools
 */
function disableDevTools() {
  document.addEventListener("keydown", function (e) {
    // F12
    if (e.keyCode === 123) {
      e.preventDefault();
      showNotification("Developer tools tidak diizinkan!", "error");
      return false;
    }

    // Ctrl+Shift+I
    if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {
      e.preventDefault();
      showNotification("Developer tools tidak diizinkan!", "error");
      return false;
    }

    // Ctrl+Shift+J
    if (e.ctrlKey && e.shiftKey && e.keyCode === 74) {
      e.preventDefault();
      showNotification("Developer tools tidak diizinkan!", "error");
      return false;
    }

    // Ctrl+U
    if (e.ctrlKey && e.keyCode === 85) {
      e.preventDefault();
      showNotification("View source tidak diizinkan!", "error");
      return false;
    }

    // Ctrl+S
    if (e.ctrlKey && e.keyCode === 83) {
      e.preventDefault();
      showNotification("Save page tidak diizinkan!", "error");
      return false;
    }
  });
}

/**
 * Disable copy paste
 */
function disableCopyPaste() {
  // Disable copy
  document.addEventListener("copy", function (e) {
    e.preventDefault();
    showNotification("Copy tidak diizinkan!", "error");
    return false;
  });

  // Disable paste
  document.addEventListener("paste", function (e) {
    e.preventDefault();
    showNotification("Paste tidak diizinkan!", "error");
    return false;
  });

  // Disable cut
  document.addEventListener("cut", function (e) {
    e.preventDefault();
    showNotification("Cut tidak diizinkan!", "error");
    return false;
  });

  // Disable text selection
  document.addEventListener("selectstart", function (e) {
    e.preventDefault();
    return false;
  });

  // Disable drag
  document.addEventListener("dragstart", function (e) {
    e.preventDefault();
    return false;
  });
}

// ==========================================
// NOTIFICATION SYSTEM
// ==========================================

/**
 * Tampilkan notifikasi
 */
function showNotification(message, type = "info") {
  const notification = document.createElement("div");
  notification.className = `notification ${type}`;
  notification.textContent = message;

  // Tambahkan ke container
  ELEMENTS.notificationContainer.appendChild(notification);

  // Trigger animation
  setTimeout(() => {
    notification.classList.add("show");
  }, 10);

  // Auto remove
  setTimeout(() => {
    notification.classList.remove("show");
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, CONFIG.ANIMATION_DURATION);
  }, CONFIG.NOTIFICATION_DURATION);
}

// ==========================================
// THEME MANAGEMENT
// ==========================================

/**
 * Initialize theme dari localStorage atau default
 */
function initializeTheme() {
  const savedTheme = localStorage.getItem("theme");
  if (savedTheme === "dark") {
    STATE.isDarkMode = true;
    document.documentElement.classList.add("dark");
    updateThemeIcons();
  } else {
    STATE.isDarkMode = false;
    document.documentElement.classList.remove("dark");
    updateThemeIcons();
  }
}

/**
 * Toggle dark/light mode
 */
function toggleTheme() {
  STATE.isDarkMode = !STATE.isDarkMode;

  if (STATE.isDarkMode) {
    document.documentElement.classList.add("dark");
    localStorage.setItem("theme", "dark");
  } else {
    document.documentElement.classList.remove("dark");
    localStorage.setItem("theme", "light");
  }

  updateThemeIcons();
}

/**
 * Update theme icons
 */
function updateThemeIcons() {
  if (STATE.isDarkMode) {
    // Dark mode: show sun icon (untuk switch ke light)
    ELEMENTS.sunIcon.classList.remove("hidden");
    ELEMENTS.moonIcon.classList.add("hidden");
    ELEMENTS.themeToggleMobile.checked = true;
  } else {
    // Light mode: show moon icon (untuk switch ke dark)
    ELEMENTS.sunIcon.classList.add("hidden");
    ELEMENTS.moonIcon.classList.remove("hidden");
    ELEMENTS.themeToggleMobile.checked = false;
  }
}

// ==========================================
// NAVIGATION MANAGEMENT
// ==========================================

/**
 * Toggle mobile menu
 */
function toggleMobileMenu() {
  STATE.isMobileMenuOpen = !STATE.isMobileMenuOpen;

  if (STATE.isMobileMenuOpen) {
    ELEMENTS.mobileMenu.classList.remove("hidden");
    ELEMENTS.mobileMenuBtn.classList.add("hamburger-active");
  } else {
    ELEMENTS.mobileMenu.classList.add("hidden");
    ELEMENTS.mobileMenuBtn.classList.remove("hamburger-active");
  }
}

/**
 * Close mobile menu
 */
function closeMobileMenu() {
  STATE.isMobileMenuOpen = false;
  ELEMENTS.mobileMenu.classList.add("hidden");
  ELEMENTS.mobileMenuBtn.classList.remove("hamburger-active");
}

/**
 * Handle navigation link clicks
 */
function handleNavigation(e) {
  e.preventDefault();
  const href = e.target.getAttribute("href");

  if (href && href.startsWith("#")) {
    const targetElement = document.querySelector(href);
    if (targetElement) {
      smoothScrollTo(targetElement);
      closeMobileMenu();
    }
  }
}

/**
 * Handle brand name click - scroll to top
 */
function handleBrandClick() {
  window.scrollTo({
    top: 0,
    behavior: "smooth",
  });
}

/**
 * Update navbar appearance on scroll
 */
function updateNavbarOnScroll() {
  const scrollY = window.scrollY;

  if (scrollY > CONFIG.SCROLL_THRESHOLD) {
    // Add glass effect
    ELEMENTS.navbar.classList.add("glass-effect");
    if (STATE.isDarkMode) {
      ELEMENTS.navbar.classList.add("glass-effect-dark");
    }

    // Show back to top button
    ELEMENTS.backToTop.classList.remove("opacity-0", "invisible");
    ELEMENTS.backToTop.classList.add("opacity-100", "visible");
  } else {
    // Remove glass effect
    ELEMENTS.navbar.classList.remove("glass-effect", "glass-effect-dark");

    // Hide back to top button
    ELEMENTS.backToTop.classList.add("opacity-0", "invisible");
    ELEMENTS.backToTop.classList.remove("opacity-100", "visible");
  }
}

// ==========================================
// LOADING MANAGEMENT
// ==========================================

/**
 * Show loading overlay
 */
function showLoading() {
  ELEMENTS.loadingOverlay.classList.add("active");
}

/**
 * Hide loading overlay
 */
function hideLoading() {
  ELEMENTS.loadingOverlay.classList.remove("active");
}

/**
 * Simulate page loading
 */
function simulateLoading(duration = 1000) {
  showLoading();
  setTimeout(() => {
    hideLoading();
  }, duration);
}

// ==========================================
// DATA MANAGEMENT
// ==========================================

/**
 * Sample data untuk skills
 */
const SKILLS_DATA = [
  {
    id: 1,
    title: "Problem Solving",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "soft",
    icon: "🧠",
  },
  {
    id: 2,
    title: "Communication",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "soft",
    icon: "💬",
  },
  {
    id: 3,
    title: "Leadership",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "soft",
    icon: "👑",
  },
  {
    id: 4,
    title: "JavaScript",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "hard",
    icon: "⚡",
  },
  {
    id: 5,
    title: "Python",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "hard",
    icon: "🐍",
  },
  {
    id: 6,
    title: "Cyber Security",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "hard",
    icon: "🔒",
  },
  {
    id: 7,
    title: "UI/UX Design",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "hard",
    icon: "🎨",
  },
  {
    id: 8,
    title: "AI Prompt Engineering",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "hard",
    icon: "🤖",
  },
];

/**
 * Sample data untuk projects
 */
const PROJECTS_DATA = [
  {
    id: 1,
    title: "AI Chatbot Assistant",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "ai",
    image: "assets/images/projects/project1.jpg",
    url: "#",
    techStack: ["Python", "TensorFlow", "OpenAI"],
  },
  {
    id: 2,
    title: "Security Audit Tool",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "cybersecurity",
    image: "assets/images/projects/project2.jpg",
    url: "#",
    techStack: ["Python", "Nmap", "Wireshark"],
  },
  {
    id: 3,
    title: "Portfolio Website",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "ui",
    image: "assets/images/projects/project3.jpg",
    url: "#",
    techStack: ["HTML", "CSS", "JavaScript"],
  },
  {
    id: 4,
    title: "Mobile App Design",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "ui",
    image: "assets/images/projects/project4.jpg",
    url: "#",
    techStack: ["Figma", "Adobe XD", "Sketch"],
  },
  {
    id: 5,
    title: "Data Analysis Dashboard",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "other",
    image: "assets/images/projects/project5.jpg",
    url: "#",
    techStack: ["Python", "Pandas", "Matplotlib"],
  },
  {
    id: 6,
    title: "E-commerce Platform",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "other",
    image: "assets/images/projects/project6.jpg",
    url: "#",
    techStack: ["React", "Node.js", "MongoDB"],
  },
];

/**
 * Sample data untuk certificates
 */
const CERTIFICATES_DATA = [
  {
    id: 1,
    title: "AI Prompt Engineering Certification",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "ai",
    image: "assets/images/certificates/cert1.jpg",
    url: "#",
    techStack: ["OpenAI", "GPT", "Prompt Design"],
  },
  {
    id: 2,
    title: "Certified Ethical Hacker",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "cybersecurity",
    image: "assets/images/certificates/cert2.jpg",
    url: "#",
    techStack: [
      "Penetration Testing",
      "Network Security",
      "Vulnerability Assessment",
    ],
  },
  {
    id: 3,
    title: "UI/UX Design Professional",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "ui",
    image: "assets/images/certificates/cert3.jpg",
    url: "#",
    techStack: ["Figma", "User Research", "Prototyping"],
  },
  {
    id: 4,
    title: "Google Cloud Professional",
    description:
      "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore.",
    category: "other",
    image: "assets/images/certificates/cert4.jpg",
    url: "#",
    techStack: ["Google Cloud", "DevOps", "Cloud Architecture"],
  },
];

/**
 * Filter data berdasarkan kategori
 */
function filterData(data, category) {
  if (category === "all") {
    return data;
  }
  return data.filter((item) => item.category === category);
}

/**
 * Paginate data
 */
function paginateData(data, page, itemsPerPage) {
  const startIndex = (page - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return data.slice(0, endIndex);
}

// ==========================================
// CONTENT RENDERING
// ==========================================

/**
 * Render skill card
 */
function renderSkillCard(skill) {
  return `
        <div class="card-base bg-light-bg dark:bg-dark-bg hover:shadow-card-hover fade-in" data-category="${skill.category}">
            <div class="text-center">
                <div class="text-4xl mb-4">${skill.icon}</div>
                <h3 class="font-heading font-heading-bold text-xl mb-3 text-light-text dark:text-dark-text">${skill.title}</h3>
                <p class="font-body text-light-text dark:text-dark-text">${skill.description}</p>
            </div>
        </div>
    `;
}

/**
 * Render project card
 */
function renderProjectCard(project) {
  return `
        <div class="card-base bg-light-bg dark:bg-dark-bg hover:shadow-card-hover fade-in" data-category="${project.category}">
            <div class="mb-4 cursor-pointer" onclick="openPreview('project', ${project.id})">
                <img src="${project.image}" alt="${project.title}" class="w-full h-48 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMzAgMTAwSDEwMFY3MEgxMzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDEwMEgxNDBWNzBIMTcwVjEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCA4MEgxMjBWNTBIMTUwVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'">
            </div>
            <h3 class="font-heading font-heading-bold text-xl mb-3 text-light-text dark:text-dark-text">${project.title}</h3>
            <p class="font-body text-light-text dark:text-dark-text mb-4">${project.description}</p>
            <a href="${project.url}" target="_blank" rel="noopener noreferrer" class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover inline-block">
                Lihat
            </a>
        </div>
    `;
}

/**
 * Render certificate card
 */
function renderCertificateCard(certificate) {
  return `
        <div class="card-base bg-light-bg dark:bg-dark-bg hover:shadow-card-hover fade-in" data-category="${certificate.category}">
            <div class="mb-4 cursor-pointer" onclick="openPreview('certificate', ${certificate.id})">
                <img src="${certificate.image}" alt="${certificate.title}" class="w-full h-48 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMzAgMTAwSDEwMFY3MEgxMzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDEwMEgxNDBWNzBIMTcwVjEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCA4MEgxMjBWNTBIMTUwVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'">
            </div>
            <h3 class="font-heading font-heading-bold text-xl mb-3 text-light-text dark:text-dark-text">${certificate.title}</h3>
            <p class="font-body text-light-text dark:text-dark-text mb-4">${certificate.description}</p>
            <a href="${certificate.url}" target="_blank" rel="noopener noreferrer" class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover inline-block">
                Lihat
            </a>
        </div>
    `;
}

/**
 * Render skills grid
 */
function renderSkills() {
  const filteredData = filterData(SKILLS_DATA, STATE.currentSkillFilter);
  const paginatedData = paginateData(
    filteredData,
    STATE.skillsPage,
    CONFIG.ITEMS_PER_PAGE
  );

  ELEMENTS.skillsGrid.innerHTML = paginatedData
    .map((skill) => renderSkillCard(skill))
    .join("");

  // Show/hide load more button
  if (paginatedData.length < filteredData.length) {
    ELEMENTS.loadMoreSkills.classList.remove("hidden");
  } else {
    ELEMENTS.loadMoreSkills.classList.add("hidden");
  }

  // Trigger fade in animation
  setTimeout(() => {
    document.querySelectorAll("#skillsGrid .fade-in").forEach((el) => {
      el.classList.add("visible");
    });
  }, 100);
}

/**
 * Render projects grid
 */
function renderProjects() {
  const filteredData = filterData(PROJECTS_DATA, STATE.currentProjectFilter);
  const paginatedData = paginateData(
    filteredData,
    STATE.projectsPage,
    CONFIG.ITEMS_PER_PAGE
  );

  ELEMENTS.projectsGrid.innerHTML = paginatedData
    .map((project) => renderProjectCard(project))
    .join("");

  // Show/hide load more button
  if (paginatedData.length < filteredData.length) {
    ELEMENTS.loadMoreProjects.classList.remove("hidden");
  } else {
    ELEMENTS.loadMoreProjects.classList.add("hidden");
  }

  // Trigger fade in animation
  setTimeout(() => {
    document.querySelectorAll("#projectsGrid .fade-in").forEach((el) => {
      el.classList.add("visible");
    });
  }, 100);
}

/**
 * Render certificates grid
 */
function renderCertificates() {
  const filteredData = filterData(
    CERTIFICATES_DATA,
    STATE.currentCertificateFilter
  );
  const paginatedData = paginateData(
    filteredData,
    STATE.certificatesPage,
    CONFIG.ITEMS_PER_PAGE
  );

  ELEMENTS.certificatesGrid.innerHTML = paginatedData
    .map((certificate) => renderCertificateCard(certificate))
    .join("");

  // Show/hide load more button
  if (paginatedData.length < filteredData.length) {
    ELEMENTS.loadMoreCertificates.classList.remove("hidden");
  } else {
    ELEMENTS.loadMoreCertificates.classList.add("hidden");
  }

  // Trigger fade in animation
  setTimeout(() => {
    document.querySelectorAll("#certificatesGrid .fade-in").forEach((el) => {
      el.classList.add("visible");
    });
  }, 100);
}

// ==========================================
// MODAL MANAGEMENT
// ==========================================

/**
 * Open preview modal
 */
function openPreview(type, id) {
  let data, item;

  if (type === "project") {
    data = PROJECTS_DATA;
  } else if (type === "certificate") {
    data = CERTIFICATES_DATA;
  }

  item = data.find((i) => i.id === id);

  if (!item) return;

  ELEMENTS.previewTitle.textContent = item.title;
  ELEMENTS.previewContent.innerHTML = `
        <div class="mb-6">
            <img src="${item.image}" alt="${item.title}" class="w-full h-64 object-cover rounded-lg" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMzAgMTAwSDEwMFY3MEgxMzBWMTAwWiIgZmlsbD0iIzlDQTNBRiIvPgo8cGF0aCBkPSJNMTcwIDEwMEgxNDBWNzBIMTcwVjEwMFoiIGZpbGw9IiM5Q0EzQUYiLz4KPHBhdGggZD0iTTE1MCA4MEgxMjBWNTBIMTUwVjgwWiIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K'">
        </div>
        <p class="font-body text-lg mb-6 text-light-text dark:text-dark-text">${item.description}</p>
        <div class="mb-6">
            <h4 class="font-heading font-heading-bold text-lg mb-3 text-light-text dark:text-dark-text">Tech Stack:</h4>
            <div class="flex flex-wrap gap-2">
                ${item.techStack
                  .map(
                    (tech) => `
                    <button class="btn-primary bg-light-card dark:bg-dark-card text-light-text dark:text-dark-text hover:bg-accent-gray hover:dark:bg-accent-gray-hover border border-light-border dark:border-dark-border text-sm">
                        ${tech}
                    </button>
                `
                  )
                  .join("")}
            </div>
        </div>
        <a href="${item.url}" target="_blank" rel="noopener noreferrer" class="btn-primary bg-light-text dark:bg-dark-text text-light-bg dark:text-dark-bg hover:bg-accent-gray hover:dark:bg-accent-gray-hover">
            Lihat ${type === "project" ? "Proyek" : "Sertifikat"}
        </a>
    `;

  ELEMENTS.previewModal.classList.add("active");
}

/**
 * Close preview modal
 */
function closePreview() {
  ELEMENTS.previewModal.classList.remove("active");
}

// ==========================================
// FORM HANDLING
// ==========================================

/**
 * Handle contact form submission
 */
function handleContactForm(e) {
  e.preventDefault();

  const formData = new FormData(e.target);
  const name = formData.get("name");
  const email = formData.get("email");
  const message = formData.get("message");

  // Validasi sederhana
  if (!name || !email || !message) {
    showNotification("Semua field harus diisi!", "error");
    return;
  }

  // Simulasi pengiriman
  showLoading();

  setTimeout(() => {
    hideLoading();
    showNotification(
      "Pesan sudah terkirim. Terimakasih atas masukkan anda",
      "success"
    );
    e.target.reset();
  }, 2000);
}

// ==========================================
// FILTER MANAGEMENT
// ==========================================

/**
 * Handle skill filter
 */
function handleSkillFilter(filter) {
  STATE.currentSkillFilter = filter;
  STATE.skillsPage = 1;

  // Update button states
  ELEMENTS.skillFilters.forEach((btn) => {
    btn.classList.remove(
      "bg-light-text",
      "dark:bg-dark-text",
      "text-light-bg",
      "dark:text-dark-bg"
    );
    btn.classList.add(
      "bg-light-card",
      "dark:bg-dark-card",
      "text-light-text",
      "dark:text-dark-text",
      "border",
      "border-light-border",
      "dark:border-dark-border"
    );
  });

  const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
  if (activeBtn) {
    activeBtn.classList.remove(
      "bg-light-card",
      "dark:bg-dark-card",
      "text-light-text",
      "dark:text-dark-text",
      "border",
      "border-light-border",
      "dark:border-dark-border"
    );
    activeBtn.classList.add(
      "bg-light-text",
      "dark:bg-dark-text",
      "text-light-bg",
      "dark:text-dark-bg"
    );
  }

  renderSkills();
}

/**
 * Handle project filter
 */
function handleProjectFilter(filter) {
  STATE.currentProjectFilter = filter;
  STATE.projectsPage = 1;

  // Update button states
  ELEMENTS.projectFilters.forEach((btn) => {
    btn.classList.remove(
      "bg-light-text",
      "dark:bg-dark-text",
      "text-light-bg",
      "dark:text-dark-bg"
    );
    btn.classList.add(
      "bg-light-card",
      "dark:bg-dark-card",
      "text-light-text",
      "dark:text-dark-text",
      "border",
      "border-light-border",
      "dark:border-dark-border"
    );
  });

  const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
  if (activeBtn) {
    activeBtn.classList.remove(
      "bg-light-card",
      "dark:bg-dark-card",
      "text-light-text",
      "dark:text-dark-text",
      "border",
      "border-light-border",
      "dark:border-dark-border"
    );
    activeBtn.classList.add(
      "bg-light-text",
      "dark:bg-dark-text",
      "text-light-bg",
      "dark:text-dark-bg"
    );
  }

  renderProjects();
}

/**
 * Handle certificate filter
 */
function handleCertificateFilter(filter) {
  STATE.currentCertificateFilter = filter;
  STATE.certificatesPage = 1;

  // Update button states
  ELEMENTS.certificateFilters.forEach((btn) => {
    btn.classList.remove(
      "bg-light-text",
      "dark:bg-dark-text",
      "text-light-bg",
      "dark:text-dark-bg"
    );
    btn.classList.add(
      "bg-light-card",
      "dark:bg-dark-card",
      "text-light-text",
      "dark:text-dark-text",
      "border",
      "border-light-border",
      "dark:border-dark-border"
    );
  });

  const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
  if (activeBtn) {
    activeBtn.classList.remove(
      "bg-light-card",
      "dark:bg-dark-card",
      "text-light-text",
      "dark:text-dark-text",
      "border",
      "border-light-border",
      "dark:border-dark-border"
    );
    activeBtn.classList.add(
      "bg-light-text",
      "dark:bg-dark-text",
      "text-light-bg",
      "dark:text-dark-bg"
    );
  }

  renderCertificates();
}

// ==========================================
// INITIALIZATION & EVENT LISTENERS
// ==========================================

/**
 * Initialize DOM elements
 */
function initializeElements() {
  // Navigation
  ELEMENTS.navbar = document.getElementById("navbar");
  ELEMENTS.brandName = document.getElementById("brandName");
  ELEMENTS.mobileMenuBtn = document.getElementById("mobileMenuBtn");
  ELEMENTS.mobileMenu = document.getElementById("mobileMenu");

  // Theme Toggle
  ELEMENTS.themeToggleDesktop = document.getElementById("themeToggleDesktop");
  ELEMENTS.themeToggleMobile = document.getElementById("themeToggleMobile");
  ELEMENTS.sunIcon = document.getElementById("sunIcon");
  ELEMENTS.moonIcon = document.getElementById("moonIcon");

  // Back to Top
  ELEMENTS.backToTop = document.getElementById("backToTop");

  // Loading
  ELEMENTS.loadingOverlay = document.getElementById("loadingOverlay");

  // Grids
  ELEMENTS.skillsGrid = document.getElementById("skillsGrid");
  ELEMENTS.projectsGrid = document.getElementById("projectsGrid");
  ELEMENTS.certificatesGrid = document.getElementById("certificatesGrid");

  // Load More Buttons
  ELEMENTS.loadMoreSkills = document.getElementById("loadMoreSkills");
  ELEMENTS.loadMoreProjects = document.getElementById("loadMoreProjects");
  ELEMENTS.loadMoreCertificates = document.getElementById(
    "loadMoreCertificates"
  );

  // Filter Buttons
  ELEMENTS.skillFilters = document.querySelectorAll(".skill-filter");
  ELEMENTS.projectFilters = document.querySelectorAll(".project-filter");
  ELEMENTS.certificateFilters = document.querySelectorAll(
    ".certificate-filter"
  );

  // Forms
  ELEMENTS.contactForm = document.getElementById("contactForm");

  // Modals
  ELEMENTS.previewModal = document.getElementById("previewModal");
  ELEMENTS.closePreview = document.getElementById("closePreview");
  ELEMENTS.previewTitle = document.getElementById("previewTitle");
  ELEMENTS.previewContent = document.getElementById("previewContent");

  // Notifications
  ELEMENTS.notificationContainer = document.getElementById(
    "notificationContainer"
  );
}

/**
 * Setup event listeners
 */
function setupEventListeners() {
  // Theme toggle events
  ELEMENTS.themeToggleDesktop.addEventListener("click", toggleTheme);
  ELEMENTS.themeToggleMobile.addEventListener("change", toggleTheme);

  // Navigation events
  ELEMENTS.brandName.addEventListener("click", handleBrandClick);
  ELEMENTS.mobileMenuBtn.addEventListener("click", toggleMobileMenu);
  ELEMENTS.backToTop.addEventListener("click", () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  });

  // Navigation links
  document.querySelectorAll('a[href^="#"]').forEach((link) => {
    link.addEventListener("click", handleNavigation);
  });

  // Scroll events
  window.addEventListener("scroll", throttle(updateNavbarOnScroll, 16));

  // Filter events
  ELEMENTS.skillFilters.forEach((btn) => {
    btn.addEventListener("click", (e) => {
      handleSkillFilter(e.target.dataset.filter);
    });
  });

  ELEMENTS.projectFilters.forEach((btn) => {
    btn.addEventListener("click", (e) => {
      handleProjectFilter(e.target.dataset.filter);
    });
  });

  ELEMENTS.certificateFilters.forEach((btn) => {
    btn.addEventListener("click", (e) => {
      handleCertificateFilter(e.target.dataset.filter);
    });
  });

  // Load more events
  ELEMENTS.loadMoreSkills.addEventListener("click", () => {
    STATE.skillsPage++;
    renderSkills();
  });

  ELEMENTS.loadMoreProjects.addEventListener("click", () => {
    STATE.projectsPage++;
    renderProjects();
  });

  ELEMENTS.loadMoreCertificates.addEventListener("click", () => {
    STATE.certificatesPage++;
    renderCertificates();
  });

  // Form events
  ELEMENTS.contactForm.addEventListener("submit", handleContactForm);

  // Modal events
  ELEMENTS.closePreview.addEventListener("click", closePreview);
  ELEMENTS.previewModal.addEventListener("click", (e) => {
    if (e.target === ELEMENTS.previewModal) {
      closePreview();
    }
  });

  // Fade in animation on scroll
  const observerOptions = {
    threshold: 0.1,
    rootMargin: "0px 0px -50px 0px",
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add("visible");
      }
    });
  }, observerOptions);

  // Observe all fade-in elements
  document.querySelectorAll(".fade-in").forEach((el) => {
    observer.observe(el);
  });
}

/**
 * Initialize application
 */
function initializeApp() {
  // Initialize DOM elements
  initializeElements();

  // Initialize theme
  initializeTheme();

  // Setup event listeners
  setupEventListeners();

  // Enable security features
  disableRightClick();
  disableDevTools();
  disableCopyPaste();

  // Render initial content
  renderSkills();
  renderProjects();
  renderCertificates();

  // Hide loading after initialization
  setTimeout(() => {
    hideLoading();
  }, 500);
}

// ==========================================
// APPLICATION START
// ==========================================

// Wait for DOM to be fully loaded
document.addEventListener("DOMContentLoaded", initializeApp);

// Global functions untuk onclick handlers
window.openPreview = openPreview;
window.closePreview = closePreview;
