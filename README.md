# Personal Portfolio - <PERSON>

## 🚀 Overview
Personal Portfolio website untuk <PERSON> S<PERSON> yang menampilkan keahlian sebagai AI Prompt Engineer, Cyber Security, dan <PERSON>.

## 🛠️ Tech Stack
- **HTML5** - Semantic markup
- **TailwindCSS v4.1.11** - Utility-first CSS framework
- **Vanilla JavaScript** - Modern ES6+ features
- **Google Fonts** - Merriweather & Open Sans

## 🎨 Design Features
- **Responsive Design** - Mobile-first approach
- **Dark/Light Mode** - Automatic theme switching
- **Smooth Animations** - CSS transitions & JavaScript animations
- **Glass Effect** - Modern glassmorphism navbar
- **Security Features** - Disable right-click, inspect, copy-paste

## 📱 Responsive Breakpoints
- **Mobile**: < 768px (Hamburger menu, toggle switch)
- **Tablet**: 768px - 1024px (Sun/moon icons)
- **Desktop**: > 1024px (Full navigation, sun/moon icons)

## 🔒 Security Features
- Disable right-click context menu
- Disable F12 and developer tools shortcuts
- Disable copy/paste/cut operations
- Disable text selection and drag
- Real-time notifications for security violations

## 🎯 Sections
1. **Hero** - Introduction with profile image
2. **About** - Personal information and social media
3. **Skills** - Filterable skills (Soft/Hard skills)
4. **Projects** - Portfolio projects with preview modal
5. **Certificates** - Achievements with preview modal
6. **Tech Stack** - Technologies used
7. **Contact** - Contact form with validation
8. **Footer** - Links and copyright

## 🚀 Getting Started

### Prerequisites
- Node.js (for TailwindCSS CLI)
- Modern web browser

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Development
1. Start TailwindCSS watch mode:
   ```bash
   npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --watch
   ```

2. Open `index.html` in your browser or use a local server

### Build for Production
```bash
npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --minify
```

## 📁 Project Structure
```
personal-portfolio/
├── assets/
│   ├── icons/
│   │   ├── social-media/
│   │   ├── ui/
│   │   └── tech-stacks/
│   ├── images/
│   │   ├── certificates/
│   │   ├── profile/
│   │   └── projects/
│   └── fonts/
├── src/
│   ├── css/
│   │   ├── input.css
│   │   ├── components/
│   │   └── utilities/
│   ├── js/
│   │   ├── components/
│   │   ├── utils/
│   │   └── security/
│   └── data/
├── dist/
│   ├── css/
│   └── js/
├── config/
│   └── tailwind.config.js
├── tools/
├── index.html
└── package.json
```

## 🎨 Color Scheme
- **Primary Black**: #181818
- **Primary White**: #FFFFFF
- **Accent Gray**: #6c757d
- **Light Theme**: White backgrounds, black text
- **Dark Theme**: Black backgrounds, white text

## 🔤 Typography
- **Headings**: Merriweather (Bold)
- **Body Text**: Open Sans (Regular)

## ✨ Features
- [x] Responsive design for all devices
- [x] Dark/Light mode toggle
- [x] Smooth scrolling navigation
- [x] Animated hamburger menu
- [x] Glass effect navbar on scroll
- [x] Back to top button
- [x] Loading animations
- [x] Hover effects on cards and buttons
- [x] Preview modals for projects/certificates
- [x] Contact form with validation
- [x] Security features
- [x] SEO optimized
- [x] Accessibility features

## 🔧 Customization
1. **Content**: Update data arrays in `dist/js/script.js`
2. **Colors**: Modify `config/tailwind.config.js`
3. **Fonts**: Update Google Fonts import in `src/css/input.css`
4. **Images**: Replace placeholder images in `assets/` folders

## 📄 License
© 2025 Muhammad Syaamil Muzhaffar. All rights reserved.

## 👨‍💻 Developer
**Muhammad Syaamil Muzhaffar**
- AI Prompt Engineer
- Cyber Security Specialist  
- UI Designer

---
*Built with ❤️ using modern web technologies*
