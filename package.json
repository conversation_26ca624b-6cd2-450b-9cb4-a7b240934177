{"name": "personal-portfolio-muh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "Personal Portfolio website untuk <PERSON> - AI Prompt Engineer, Cyber Security, UI Designer", "main": "index.html", "scripts": {"dev": "node tools/dev.js", "build": "node tools/build.js", "build:css": "npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css", "build:css:watch": "npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --watch", "build:css:minify": "npx tailwindcss -i ./src/css/input.css -o ./dist/css/output.css --minify", "format": "prettier --write .", "format:check": "prettier --check ."}, "keywords": ["portfolio", "personal-website", "ai-prompt-engineer", "cyber-security", "ui-designer", "tailwindcss", "responsive-design", "dark-mode"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@tailwindcss/cli": "^4.1.11", "tailwindcss": "^4.1.11"}, "devDependencies": {"prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/muham<PERSON><PERSON><PERSON><PERSON><PERSON>/personal-portfolio.git"}, "bugs": {"url": "https://github.com/muham<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>/personal-portfolio/issues"}, "homepage": "https://muhammadsyaamilmuzhaffar.github.io/personal-portfolio"}