/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx,css}",
    "./assets/**/*.{html,js}",
  ],
  darkMode: "class", // Enable dark mode with class strategy
  theme: {
    extend: {
      // Custom Colors - Sesuai spesifikasi: Hitam #181818, Putih #FFFFFF
      colors: {
        primary: {
          black: "#181818",
          white: "#FFFFFF",
        },
        dark: {
          bg: "#181818",
          text: "#FFFFFF",
          card: "#2a2a2a",
          border: "#404040",
        },
        light: {
          bg: "#FFFFFF",
          text: "#181818",
          card: "#f8f9fa",
          border: "#e9ecef",
        },
        accent: {
          gray: "#6c757d",
          "gray-hover": "#5a6268",
        },
      },

      // Custom Fonts - Sesuai spesifikasi: Merriweather (Bold) untuk judul, Open Sans (Regular) untuk konten
      fontFamily: {
        heading: ["Merriweather", "serif"], // Untuk judul
        body: ["Open Sans", "sans-serif"], // Untuk isi konten
      },

      // Custom Font Weights
      fontWeight: {
        "heading-bold": "700", // Bold untuk Merriweather
        "body-regular": "400", // Regular untuk Open Sans
      },

      // Custom Spacing untuk presisi layout
      spacing: {
        18: "4.5rem",
        88: "22rem",
        128: "32rem",
      },

      // Custom Border Radius untuk konsistensi
      borderRadius: {
        card: "12px",
        button: "8px",
        icon: "50%",
      },

      // Custom Shadows untuk efek hover dan card
      boxShadow: {
        card: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
        "card-hover":
          "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        button: "0 2px 4px rgba(0, 0, 0, 0.1)",
        "button-hover": "0 4px 8px rgba(0, 0, 0, 0.15)",
      },

      // Custom Transitions untuk smooth animations
      transitionDuration: {
        400: "400ms",
        600: "600ms",
      },

      // Custom Z-index untuk layering
      zIndex: {
        navbar: "50",
        modal: "100",
        notification: "110",
      },

      // Custom Backdrop Blur untuk glass effect
      backdropBlur: {
        navbar: "10px",
      },
    },
  },
  plugins: [
    // Plugin untuk custom utilities
    function ({ addUtilities, addComponents, theme }) {
      // Custom utilities untuk glass effect navbar
      addUtilities({
        ".glass-effect": {
          "backdrop-filter": "blur(10px)",
          "background-color": "rgba(255, 255, 255, 0.8)",
          border: "1px solid rgba(255, 255, 255, 0.2)",
        },
        ".glass-effect-dark": {
          "backdrop-filter": "blur(10px)",
          "background-color": "rgba(24, 24, 24, 0.8)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
        },

        // Smooth scrolling utility
        ".smooth-scroll": {
          "scroll-behavior": "smooth",
        },

        // Disable text selection untuk security
        ".no-select": {
          "-webkit-user-select": "none",
          "-moz-user-select": "none",
          "-ms-user-select": "none",
          "user-select": "none",
        },

        // Disable context menu
        ".no-context": {
          "-webkit-touch-callout": "none",
          "-webkit-user-select": "none",
          "-khtml-user-select": "none",
          "-moz-user-select": "none",
          "-ms-user-select": "none",
          "user-select": "none",
        },
      });

      // Custom components
      addComponents({
        // Button component styles
        ".btn-primary": {
          padding: "12px 24px",
          "border-radius": theme("borderRadius.button"),
          "font-weight": "500",
          transition: "all 0.3s ease",
          cursor: "pointer",
        },

        // Card component styles
        ".card-base": {
          "border-radius": theme("borderRadius.card"),
          padding: "24px",
          transition: "all 0.3s ease",
          "box-shadow": theme("boxShadow.card"),
        },

        // Icon styles
        ".icon-social": {
          width: "40px",
          height: "40px",
          "border-radius": theme("borderRadius.icon"),
          transition: "all 0.3s ease",
          cursor: "pointer",
        },
      });
    },
  ],
};
